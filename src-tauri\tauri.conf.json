{"$schema": "https://schema.tauri.app/config/2", "productName": "tauri-app", "version": "0.1.0", "identifier": "com.connor.tauri-app", "build": {"beforeDevCommand": "trunk serve", "devUrl": "http://localhost:1420", "beforeBuildCommand": "trunk build", "frontendDist": "../dist"}, "app": {"withGlobalTauri": true, "windows": [{"title": "tauri-app-2", "width": 1920, "height": 1080}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}