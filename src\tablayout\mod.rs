use leptos::{prelude::*};

use crate::pages::calendar::Calendar;

#[component]
pub fn TabLayout() -> impl IntoView {
    let (active_tab, set_active_tab) = signal(0);

    let tabs = vec![
        ("Tab 1", "calendar"),
        ("Tab 2", "patients"),
        ("Tab 3", "tasks"),
        ("Tab 4", "settings"),
        ("Tab 5", "help"),
    ];

    let render_tabs = view! {
        <div>
            {(0..tabs.len())
                .map(|index| view! { 
                    <button
                        on:click=move |_| set_active_tab.set(index)
                        class:selected=move || active_tab.get() == index
                    >{tabs[index].0.clone()} {tabs[index].1.clone()}
                    </button>
                })
                .collect_view()}
        </div>
    };

    let tab_content = view! {
        <div>
            <Show when=move || active_tab.get() == 0><Calendar /></Show>            
            <Show when=move || active_tab.get() == 1><Patients /></Show>
            <Show when=move || active_tab.get() == 2><Tasks /></Show>
            <Show when=move || active_tab.get() == 3><Settings /></Show>
            <Show when=move || active_tab.get() == 4><Help /></Show>
        </div>
    };
    
    view! { 
        <div>
            {render_tabs}
            {tab_content}            
        </div>    
    }
}