use leptos::{ev::MouseEvent, prelude::*};

#[component]
pub fn Counter() -> impl IntoView {
    let (count, set_count) = signal(0);
    
    let update_count = move |ev: MouseEvent| {
        ev.prevent_default();
        *set_count.write() += 1;
    };

    view! {
        <button 
            on:click=update_count
            style="width: 30%; align-self: center"
        >
            "You have clicked this button " 
            { move || count.get() } 
            " times"
        </button>
    }
}